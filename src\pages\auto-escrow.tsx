import { useState } from 'react';

// Define type for view selection props
type ViewSelectorProps = {
  setActiveView: (view: string) => void;
}

// Main App Component
export default function App() {
  // State to manage which content view is active
  const [activeView, setActiveView] = useState('home');

  // A simple mapping of views to their content components
  const renderContent = () => {
    switch (activeView) {
      case 'create':
        return <CreateDealView />;
      case 'active':
        return <ActiveDealsView />;
      case 'archive':
        return <ArchiveDealsView />;
      case 'terms':
        return <TermsAndConditionsView />;
      default:
        return <HomeView setActiveView={setActiveView} />;
    }
  };

  return (
    <div className="min-h-screen font-sans text-slate-200 p-3 sm:p-4 lg:p-6" style={{ backgroundColor: '#101828' }}>
      <div className="max-w-6xl mx-auto">
        {/* Main Content */}
        <main className="w-full">
          {activeView !== 'home' && (
            <button
              onClick={() => setActiveView('home')}
              className="mb-4 text-slate-400 hover:text-slate-200 transition-colors duration-200 flex items-center gap-2 bg-slate-800/60 hover:bg-slate-800 px-3 py-2 rounded-md text-sm"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="m15 18-6-6 6-6"/></svg>
              Back
            </button>
          )}
          {renderContent()}
        </main>
      </div>
    </div>
  );
}

// Home View Component (The main screen from the image)
const HomeView = ({ setActiveView }: ViewSelectorProps) => (
  <div className="flex flex-col items-center justify-center min-h-[80vh] px-4">
    <div className="text-center mb-8">
      <h1 className="text-3xl md:text-4xl lg:text-5xl font-semibold text-white mb-3 tracking-tight">
        Auto-Escrow
      </h1>
      <p className="text-slate-400 text-sm md:text-base">
        Secure transactions made simple
      </p>
    </div>

    <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 w-full max-w-2xl mb-6">
      <button
        onClick={() => setActiveView('create')}
        className="bg-slate-800/50 hover:bg-slate-700/50 border border-slate-700/50 hover:border-slate-600 text-slate-200 font-medium py-3 px-4 rounded-lg transition-all duration-200 text-sm"
      >
        Create Deal
      </button>
      <button
        onClick={() => setActiveView('active')}
        className="bg-slate-800/50 hover:bg-slate-700/50 border border-slate-700/50 hover:border-slate-600 text-slate-200 font-medium py-3 px-4 rounded-lg transition-all duration-200 text-sm"
      >
        Active Deals
      </button>
      <button
        onClick={() => setActiveView('archive')}
        className="bg-slate-800/50 hover:bg-slate-700/50 border border-slate-700/50 hover:border-slate-600 text-slate-200 font-medium py-3 px-4 rounded-lg transition-all duration-200 text-sm"
      >
        Archive
      </button>
    </div>

    <button
      onClick={() => setActiveView('terms')}
      className="text-slate-500 hover:text-slate-300 text-xs underline underline-offset-4 transition-colors duration-200"
    >
      Terms and Conditions
    </button>
  </div>
);

// Create Deal View Component
const CreateDealView = () => {
  const [dealType, setDealType] = useState('goods');
  const [amount, setAmount] = useState('');
  const [description, setDescription] = useState('');
  const [buyerEmail, setBuyerEmail] = useState('');
  const [sellerEmail, setSellerEmail] = useState('');

  return (
    <div className="text-slate-200 max-w-3xl mx-auto">
      <h2 className="text-2xl font-semibold mb-6 text-white">Create New Deal</h2>

      <div className="bg-slate-800/40 border border-slate-700/50 rounded-xl p-6">
        <form className="space-y-5">
          {/* Deal Type Selection */}
          <div>
            <label className="block text-sm font-medium text-slate-300 mb-3">Deal Type</label>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              {[
                { value: 'goods', label: 'Physical Goods', icon: '📦' },
                { value: 'digital', label: 'Digital Products', icon: '💻' },
                { value: 'services', label: 'Services', icon: '🛠️' }
              ].map((type) => (
                <button
                  key={type.value}
                  type="button"
                  onClick={() => setDealType(type.value)}
                  className={`p-3 rounded-lg border transition-all duration-200 text-sm ${
                    dealType === type.value
                      ? 'border-blue-500/50 bg-blue-500/10 text-blue-400'
                      : 'border-slate-600/50 hover:border-slate-500 text-slate-300 hover:bg-slate-700/30'
                  }`}
                >
                  <div className="text-lg mb-1">{type.icon}</div>
                  <div className="font-medium">{type.label}</div>
                </button>
              ))}
            </div>
          </div>

          {/* Amount */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">Deal Amount (USD)</label>
              <input
                type="number"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                placeholder="0.00"
                className="w-full bg-slate-700/40 border border-slate-600/50 rounded-lg text-white placeholder-slate-400 py-2.5 px-3 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">Escrow Fee</label>
              <div className="bg-slate-700/20 border border-slate-600/50 rounded-lg py-2.5 px-3 text-slate-400 text-sm">
                ${amount ? (parseFloat(amount) * 0.025).toFixed(2) : '0.00'} (2.5%)
              </div>
            </div>
          </div>

          {/* Participants */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">Buyer Email</label>
              <input
                type="email"
                value={buyerEmail}
                onChange={(e) => setBuyerEmail(e.target.value)}
                placeholder="<EMAIL>"
                className="w-full bg-slate-700/40 border border-slate-600/50 rounded-lg text-white placeholder-slate-400 py-2.5 px-3 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">Seller Email</label>
              <input
                type="email"
                value={sellerEmail}
                onChange={(e) => setSellerEmail(e.target.value)}
                placeholder="<EMAIL>"
                className="w-full bg-slate-700/40 border border-slate-600/50 rounded-lg text-white placeholder-slate-400 py-2.5 px-3 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm"
              />
            </div>
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-slate-300 mb-2">Deal Description</label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Describe what is being bought/sold and any specific terms..."
              rows={3}
              className="w-full bg-slate-700/40 border border-slate-600/50 rounded-lg text-white placeholder-slate-400 py-2.5 px-3 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-none text-sm"
            />
          </div>

          {/* Submit Button */}
          <div className="flex justify-center pt-2">
            <button
              type="submit"
              className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2.5 px-6 rounded-lg transition-colors duration-200 text-sm"
            >
              Create Deal
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// Active Deals View Component
const ActiveDealsView = () => {
  const activeDeals = [
    {
      id: 'ESC-001',
      amount: 250.00,
      type: 'Digital Products',
      buyer: '<EMAIL>',
      seller: '<EMAIL>',
      status: 'Pending Payment',
      created: '2023-11-15',
      description: 'Website design package'
    },
    {
      id: 'ESC-002',
      amount: 1200.00,
      type: 'Physical Goods',
      buyer: '<EMAIL>',
      seller: '<EMAIL>',
      status: 'Awaiting Delivery',
      created: '2023-11-12',
      description: 'Gaming laptop'
    },
    {
      id: 'ESC-003',
      amount: 75.00,
      type: 'Services',
      buyer: '<EMAIL>',
      seller: '<EMAIL>',
      status: 'In Progress',
      created: '2023-11-10',
      description: 'Logo design service'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Pending Payment': return 'bg-amber-500/15 text-amber-400 border-amber-500/30';
      case 'Awaiting Delivery': return 'bg-blue-500/15 text-blue-400 border-blue-500/30';
      case 'In Progress': return 'bg-emerald-500/15 text-emerald-400 border-emerald-500/30';
      default: return 'bg-slate-500/15 text-slate-400 border-slate-500/30';
    }
  };

  return (
    <div className="text-slate-200">
      <h2 className="text-2xl font-semibold mb-6 text-white">Active Deals</h2>

      <div className="space-y-4">
        {activeDeals.map((deal) => (
          <div key={deal.id} className="bg-slate-800/40 border border-slate-700/50 rounded-xl p-5 hover:border-slate-600/50 transition-colors">
            <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  <h3 className="text-lg font-medium text-white">#{deal.id}</h3>
                  <span className={`px-2 py-1 rounded-md text-xs font-medium border ${getStatusColor(deal.status)}`}>
                    {deal.status}
                  </span>
                </div>
                <p className="text-slate-300 mb-3 text-sm">{deal.description}</p>
                <div className="flex flex-wrap gap-4 text-xs text-slate-400">
                  <span>{deal.type}</span>
                  <span>Created {new Date(deal.created).toLocaleDateString()}</span>
                  <span>Buyer: {deal.buyer}</span>
                  <span>Seller: {deal.seller}</span>
                </div>
              </div>
              <div className="flex flex-col lg:items-end gap-2">
                <div className="text-xl font-semibold text-emerald-400">${deal.amount.toFixed(2)}</div>
                <button className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1.5 rounded-lg transition-colors text-sm">
                  View Details
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Archive Deals View Component
const ArchiveDealsView = () => {
  const archivedDeals = [
    {
      id: 'ESC-098',
      amount: 450.00,
      type: 'Digital Products',
      buyer: '<EMAIL>',
      seller: '<EMAIL>',
      status: 'Completed',
      completed: '2023-11-08',
      description: 'Mobile app development'
    },
    {
      id: 'ESC-097',
      amount: 180.00,
      type: 'Physical Goods',
      buyer: '<EMAIL>',
      seller: '<EMAIL>',
      status: 'Completed',
      completed: '2023-11-05',
      description: 'Vintage camera'
    },
    {
      id: 'ESC-096',
      amount: 320.00,
      type: 'Services',
      buyer: '<EMAIL>',
      seller: '<EMAIL>',
      status: 'Cancelled',
      completed: '2023-11-02',
      description: 'Content writing package'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Completed': return 'bg-emerald-500/15 text-emerald-400 border-emerald-500/30';
      case 'Cancelled': return 'bg-red-500/15 text-red-400 border-red-500/30';
      default: return 'bg-slate-500/15 text-slate-400 border-slate-500/30';
    }
  };

  return (
    <div className="text-slate-200">
      <h2 className="text-2xl font-semibold mb-6 text-white">Archived Deals</h2>

      <div className="space-y-4">
        {archivedDeals.map((deal) => (
          <div key={deal.id} className="bg-slate-800/25 border border-slate-700/40 rounded-xl p-5">
            <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  <h3 className="text-lg font-medium text-slate-300">#{deal.id}</h3>
                  <span className={`px-2 py-1 rounded-md text-xs font-medium border ${getStatusColor(deal.status)}`}>
                    {deal.status}
                  </span>
                </div>
                <p className="text-slate-400 mb-3 text-sm">{deal.description}</p>
                <div className="flex flex-wrap gap-4 text-xs text-slate-500">
                  <span>{deal.type}</span>
                  <span>Completed {new Date(deal.completed).toLocaleDateString()}</span>
                  <span>Buyer: {deal.buyer}</span>
                  <span>Seller: {deal.seller}</span>
                </div>
              </div>
              <div className="flex flex-col lg:items-end gap-2">
                <div className="text-xl font-semibold text-slate-400">${deal.amount.toFixed(2)}</div>
                <button className="bg-slate-600 hover:bg-slate-500 text-white px-3 py-1.5 rounded-lg transition-colors text-sm">
                  View Details
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Terms and Conditions View Component
const TermsAndConditionsView = () => (
    <div className="text-left max-w-4xl mx-auto text-slate-200">
        <h2 className="text-2xl font-semibold mb-6 text-white">Terms and Conditions</h2>

        <div className="bg-slate-800/40 border border-slate-700/50 rounded-xl p-6">
            <div className="space-y-5 text-slate-300">
                <div className="bg-blue-500/10 border border-blue-500/30 rounded-lg p-4 mb-5">
                    <p className="text-blue-400 font-medium text-sm">
                        Last Updated: November 15, 2023
                    </p>
                    <p className="text-xs text-slate-300 mt-1">
                        Please read these terms carefully before using our Auto-Escrow service.
                    </p>
                </div>

                <section>
                    <h3 className="text-lg font-medium text-white mb-3 flex items-center">
                        <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-medium mr-2">1</span>
                        Introduction
                    </h3>
                    <p className="mb-3 text-sm">
                        Welcome to Auto-Escrow, a secure transaction platform operated by RMarket. These terms and conditions ("Terms") outline the rules and regulations for the use of our escrow services. By using our service, you agree to be bound by these Terms.
                    </p>
                    <p className="text-sm">
                        Our escrow service acts as a neutral third party to facilitate secure transactions between buyers and sellers, ensuring that funds are only released when all agreed-upon conditions are met.
                    </p>
                </section>

                <section>
                    <h3 className="text-lg font-medium text-white mb-3 flex items-center">
                        <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-medium mr-2">2</span>
                        Service Description
                    </h3>
                    <p className="mb-3 text-sm">
                        Auto-Escrow provides a secure platform for holding funds in a transaction between two or more parties until specified conditions are met. Our services include:
                    </p>
                    <ul className="list-disc list-inside space-y-1 ml-4 mb-3 text-sm">
                        <li>Secure fund holding and management</li>
                        <li>Transaction monitoring and verification</li>
                        <li>Dispute resolution assistance</li>
                        <li>Automated release of funds upon completion</li>
                        <li>Transaction history and reporting</li>
                    </ul>
                </section>

                <section>
                    <h3 className="text-lg font-medium text-white mb-3 flex items-center">
                        <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-medium mr-2">3</span>
                        User Responsibilities
                    </h3>
                    <p className="mb-3 text-sm">
                        Users are responsible for:
                    </p>
                    <ul className="list-disc list-inside space-y-1 ml-4 mb-3 text-sm">
                        <li>Providing accurate and complete information</li>
                        <li>Complying with all applicable laws and regulations</li>
                        <li>Maintaining the confidentiality of account credentials</li>
                        <li>Promptly reporting any suspicious activity</li>
                        <li>Ensuring all transaction details are correct before confirmation</li>
                    </ul>
                </section>

                <section>
                    <h3 className="text-lg font-medium text-white mb-3 flex items-center">
                        <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-medium mr-2">4</span>
                        Fees and Charges
                    </h3>
                    <p className="mb-3 text-sm">
                        Our escrow service charges a fee of 2.5% of the transaction amount. This fee covers:
                    </p>
                    <ul className="list-disc list-inside space-y-1 ml-4 mb-3 text-sm">
                        <li>Secure fund management and storage</li>
                        <li>Transaction processing and verification</li>
                        <li>Customer support and dispute resolution</li>
                        <li>Platform maintenance and security</li>
                    </ul>
                    <p className="text-sm">
                        Fees are automatically deducted from the transaction amount before release to the seller.
                    </p>
                </section>

                <section>
                    <h3 className="text-lg font-medium text-white mb-3 flex items-center">
                        <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-medium mr-2">5</span>
                        Dispute Resolution
                    </h3>
                    <p className="mb-3 text-sm">
                        In case of disputes between parties, Auto-Escrow will:
                    </p>
                    <ul className="list-disc list-inside space-y-1 ml-4 mb-3 text-sm">
                        <li>Review all available evidence and documentation</li>
                        <li>Communicate with both parties to understand the issue</li>
                        <li>Make a fair determination based on the evidence</li>
                        <li>Release funds according to the resolution decision</li>
                    </ul>
                </section>

                <section>
                    <h3 className="text-lg font-medium text-white mb-3 flex items-center">
                        <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-medium mr-2">6</span>
                        Limitation of Liability
                    </h3>
                    <p className="mb-3 text-sm">
                        Auto-Escrow's liability is limited to the amount of funds held in escrow for the specific transaction. We are not liable for:
                    </p>
                    <ul className="list-disc list-inside space-y-1 ml-4 mb-3 text-sm">
                        <li>Indirect, incidental, or consequential damages</li>
                        <li>Loss of profits or business opportunities</li>
                        <li>Actions or omissions of transaction parties</li>
                        <li>Technical issues beyond our reasonable control</li>
                    </ul>
                </section>

                <section>
                    <h3 className="text-lg font-medium text-white mb-3 flex items-center">
                        <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-medium mr-2">7</span>
                        Privacy and Security
                    </h3>
                    <p className="mb-3 text-sm">
                        We are committed to protecting your privacy and maintaining the security of your transactions. All personal and financial information is encrypted and stored securely. We do not share your information with third parties except as required by law or for transaction processing.
                    </p>
                </section>

                <section>
                    <h3 className="text-lg font-medium text-white mb-3 flex items-center">
                        <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-medium mr-2">8</span>
                        Termination
                    </h3>
                    <p className="mb-3 text-sm">
                        We reserve the right to terminate or suspend access to our services for violations of these Terms or for any other reason at our sole discretion. Upon termination, any funds held in escrow will be handled according to the status of ongoing transactions.
                    </p>
                </section>

                <div className="bg-slate-700/30 rounded-lg p-4 mt-6">
                    <h4 className="text-base font-medium text-white mb-2">Contact Information</h4>
                    <p className="text-slate-300 text-sm mb-2">
                        If you have any questions about these Terms, please contact our support team at:
                    </p>
                    <div className="space-y-1 text-sm">
                        <p>Email: <EMAIL></p>
                        <p>Phone: +1 (555) 123-ESCROW</p>
                        <p>Hours: Monday - Friday, 9:00 AM - 6:00 PM EST</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
);
